import {
  DndContext,
  closestCenter,
  closestCorners,
  useSensor,
  useSensors,
  PointerSensor,
  DragOverlay,
  type DragStartEvent,
  type DragOverEvent,
  type DragEndEvent,
} from "@dnd-kit/core";
import { restrictToParentElement } from "@dnd-kit/modifiers";
import {
  SortableContext,
  useSortable,
  arrayMove,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useState } from "react";
import { createPortal } from "react-dom";

const SortableItem = ({
  id,
  styles = { padding: 10, width: 200, border: "1px solid black" },
  children,
}: {
  id: string;
  styles?: React.CSSProperties;
  children?: React.ReactNode;
}) => {
  const {
    attributes,
    listeners,
    transform,
    transition,
    isDragging,
    setNodeRef,
  } = useSortable({ id });

  return (
    <div
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      style={{
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
        ...styles,
      }}
    >
      {children || `item ${id}`}
    </div>
  );
};

const SortContainer = ({ items }: { items: string[] }) => {
  return (
    <SortableItem
      id="container"
      styles={{
        padding: 10,
        border: "1px solid black",
      }}
    >
      <SortableContext items={items}>
        {items.map((item) => (
          <SortableItem
            key={item}
            id={item}
            styles={{ width: 50, height: 50, border: "1px solid black" }}
          />
        ))}
      </SortableContext>
    </SortableItem>
  );
};

const SortableSample = () => {
  const [items, setItems] = useState(["1", "2", "3", "4", ["5", "6"]]);
  const [activeId, setActiveId] = useState(null);
  const [overId, setOverId] = useState(null);

  // const sensors = useSensors(useSensor(PointerSensor));

  const handleDragStart = (event: DragStartEvent) => {
    console.log(event.active.id, "start");
    setActiveId(event.active.id);
  };

  const handleDragOver = (event: DragOverEvent) => {
    console.log(event.over?.id, "over");
    setOverId(event.over?.id);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    setActiveId(null);
    setOverId(null);
    const { active, over } = event;
    console.log(active.id, over?.id, event, "end");
    if (!active || !over) return; // 处理边界情况
    if (active.id !== over?.id) {
      const oldIndex = items.indexOf(active.id as string);
      const newIndex = items.indexOf(over?.id as string);
      setItems(arrayMove(items, oldIndex, newIndex));
    }
  };

  const handleDragMove = (event: DragOverEvent) => {
    console.log(event, "move");
  };

  return (
    <DndContext
      collisionDetection={closestCorners}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
      onDragMove={handleDragMove}
    >
      <SortableContext items={items.map((i) => i.toString())}>
        {items.map((item) => {
          if (item instanceof Array) {
            return <SortContainer key={item.toString()} items={item} />;
          } else {
            return <SortableItem key={item} id={item} />;
          }
        })}
      </SortableContext>
      {/* <SortableContext items={items}>
        {items.map((item) => (
          <SortableItem key={item} id={item} />
        ))}
      </SortableContext> */}
      {createPortal(
        <DragOverlay dropAnimation={null}>
          {activeId ? (
            <div
              style={{
                padding: "8px",
                border: "1px dashed gray",
                background: "#eee",
              }}
            >
              Item {activeId}
            </div>
          ) : null}
        </DragOverlay>,
        document.body
      )}
    </DndContext>
  );
};

export default SortableSample;
