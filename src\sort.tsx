import {
  DndContext,
  closestCenter,
  closestCorners,
  DragOverlay,
  type DragStartEvent,
  type DragOverEvent,
  type DragEndEvent,
  type CollisionDetection,
  type UniqueIdentifier,
} from "@dnd-kit/core";
import { SortableContext, useSortable, arrayMove } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useState } from "react";
import { createPortal } from "react-dom";

const SortableItem = ({
  id,
  styles = { padding: 10, width: 200, border: "1px solid black" },
  children,
}: {
  id: string;
  styles?: React.CSSProperties;
  children?: React.ReactNode;
}) => {
  const {
    attributes,
    listeners,
    transform,
    transition,
    isDragging,
    setNodeRef,
  } = useSortable({ id });

  return (
    <div
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      style={{
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
        ...styles,
      }}
    >
      {children || `item ${id}`}
    </div>
  );
};

const SortContainer = ({ id, items }: { id: string; items: string[] }) => {
  return (
    <SortableItem
      id={id}
      styles={{
        width: 200,
        padding: 10,
        border: "1px solid black",
      }}
    >
      <SortableContext items={items}>
        {items.map((item) => (
          <SortableItem
            key={item}
            id={item}
            styles={{ width: 50, height: 50, border: "1px solid black" }}
          />
        ))}
      </SortableContext>
    </SortableItem>
  );
};

// 自定义碰撞检测算法，解决嵌套排序问题
const customCollisionDetection: CollisionDetection = (args) => {
  const { active, droppableContainers } = args;

  // 如果拖拽的是container，我们需要特殊处理
  if (typeof active.id === "string" && active.id.startsWith("container-")) {
    // 过滤掉所有container，只检测外层的排序项
    const filteredContainers = droppableContainers.filter(
      (container) => !container.id.toString().startsWith("container-")
    );

    // 使用closestCenter算法检测与外层项目的碰撞
    return closestCorners({
      ...args,
      droppableContainers: filteredContainers,
    });
  }

  // 对于其他项目，使用默认的closestCorners算法
  return closestCenter(args);
};

const SortableSample = () => {
  const [items, setItems] = useState<(string | string[])[]>([
    "1",
    "2",
    "3",
    "4",
    ["5", "6"],
  ]);
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);

  // const sensors = useSensors(useSensor(PointerSensor));

  const handleDragStart = (event: DragStartEvent) => {
    console.log(event.active.id, "start");
    setActiveId(event.active.id);
  };

  const handleDragOver = (event: DragOverEvent) => {
    console.log(event.over?.id, "over");
  };

  const handleDragEnd = (event: DragEndEvent) => {
    setActiveId(null);
    const { active, over } = event;
    console.log(active.id, over?.id, event, "end");
    if (!active || !over) return; // 处理边界情况
    if (active.id !== over?.id) {
      // 找到拖拽项和目标项的索引
      const oldIndex = items.findIndex((item, index) => {
        if (item instanceof Array) {
          return `container-${index}` === active.id;
        }
        return item === active.id;
      });

      const newIndex = items.findIndex((item, index) => {
        if (item instanceof Array) {
          return `container-${index}` === over.id;
        }
        return item === over.id;
      });

      if (oldIndex !== -1 && newIndex !== -1) {
        setItems(arrayMove(items, oldIndex, newIndex));
      }
    }
  };

  const handleDragMove = (event: DragOverEvent) => {
    console.log(event, "move");
  };

  return (
    <DndContext
      collisionDetection={customCollisionDetection}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
      onDragMove={handleDragMove}
    >
      <SortableContext
        items={items.map((i, index) =>
          i instanceof Array ? `container-${index}` : i.toString()
        )}
      >
        {items.map((item, index) => {
          if (item instanceof Array) {
            return (
              <SortContainer
                key={`container-${index}`}
                id={`container-${index}`}
                items={item}
              />
            );
          } else {
            return <SortableItem key={item} id={item} />;
          }
        })}
      </SortableContext>
      {/* <SortableContext items={items}>
        {items.map((item) => (
          <SortableItem key={item} id={item} />
        ))}
      </SortableContext> */}
      {createPortal(
        <DragOverlay dropAnimation={null}>
          {activeId ? (
            <div
              style={{
                padding: "8px",
                border: "1px dashed gray",
                background: "#eee",
              }}
            >
              Item {activeId}
            </div>
          ) : null}
        </DragOverlay>,
        document.body
      )}
    </DndContext>
  );
};

export default SortableSample;
